<?php

  use function PHP81_BC\strftime;

  class DateTimeHelper {

    /**
     * Check if valid english date. Can validate date and datetime
     * @param string|null $date
     * @return bool
     */
    public static function isDate(?string $date): bool {
      if(empty($date)) return false;
      return DateTimeHelper::validate($date);
    }

    /**
     * Check if valid dutch date. d-m-Y
     * @param string|null $date
     * @return bool
     */
    public static function isDutchDate(?string $date): bool {
      if(empty($date)) return false;
      return DateTimeHelper::validate($date, 'd-m-Y');
    }

    /**
     * Check if valid english datetime, default format Y-m-d H:i:s
     * @param string|null $date
     * @param string $format
     * @return bool
     */
    public static function isDateTime(?string $date, string $format = 'Y-m-d H:i:s'): bool {
      if(empty($date)) return false;
      return DateTimeHelper::validate($date, $format);
    }

    /**
     * Check if valid dutch datetime, default format d-m-Y H:i:s
     * @param string|null $date
     * @param string $format
     * @return bool
     */
    public static function isDutchDateTime(?string $date, string $format = 'd-m-Y H:i:s'): bool {
      if(empty($date)) return false;
      return DateTimeHelper::validate($date, 'Y-m-d H:i:s');
    }

    /**
     * Convert a Unix timestamp to a date string
     * @param string $timestamp
     * @return bool|string
     */
    public static function convertTimestampToDateString(string $timestamp, string $format = 'Y-m-d'): bool|string {
      $date = new DateTime();
      $date->setTimestamp($timestamp);
      return  $date->format($format);
    }

    /**
     * Converts a date from one format to another format
     *
     * @param string $date_string The date to format
     * @param string $current_format Current format, eg d-m-Y
     * @param string $new_format New format, eg Y-m-d
     * @return string Converted date
     */
    public static function convertFormat($date_string, $current_format, $new_format) {
      if ($date_string == null) {
        return $date_string;
      }
      $new_date = DateTime::createFromFormat($current_format, $date_string);
      // one of the given parameters is wrong, just return the string
      if ($new_date === false) {
        return $date_string;
      }
      return $new_date->format($new_format);
    }

    /**
     * Converts a date from MYSQL format to European
     *
     * @param string $date_string The date to format
     * @return string Converted date
     */
    public static function convertMysqlDatetimeToEuropeanFormat($date_string, $include_time = false) {
      if ($include_time) {
        return self::convertFormat($date_string, "Y-m-d H:i:s", "d-m-Y H:i:s");
      }
      else {
        return self::convertFormat($date_string, "Y-m-d H:i:s", "d-m-Y");
      }
    }

    /**
     * Get the name of a month based on the month number
     *
     * @param int $month_nr Number of the month (starting with 1)
     * @return string Name of the month, in the language of the system
     */
    public static function getMonthName($month_nr) {
      return ucfirst(DateTimeHelper::strftime('%B', strtotime(date('Y') . '-' . $month_nr . '-01')));
    }


    /**
     * Get month names as array
     * @return array [monthnr=>monthname]
     */
    public static function getMonths() {
      $months = [];
      for ($tel = 1; $tel <= 12; $tel++) {
        $months[$tel] = DateTimeHelper::getMonthName($tel);
      }
      return $months;
    }

    /**
     * Get the name of a day based on the date
     *
     * @param string $date Date in the format of 'Y-m-d'
     * @return string Name of the day, in the language of the system
     */
    public static function getDayName(string $date) {
      return ucfirst(DateTimeHelper::strftime('%A', strtotime($date)));
    }


    /**
     * Validate a date, datetime, or time string with the given format
     * @param string|null $date
     * @param string $format
     * @return bool
     */
    public static function validate(?string $date, string $format = 'Y-m-d'): bool {
      if(empty($date)) return false;
      $d = DateTime::createFromFormat($format, $date);
      return $d && $d->format($format) == $date;
    }

    /**
     * Convert time to string which you can be used in javascript Date() function
     * Example: var date = new Date("<?php echo DateTimeHelper::getJSFriendlyDate() ?>")
     * @param bool|int $time
     * @return false|string
     */
    public static function getJSFriendlyDate($time = false) {
      if (!$time) {
        $time = time();
      }
      return date("D M d Y H:i:s O", $time);
    }

    /**
     * Get monday by week number, format 2019-06-10
     * @param      $iWeek : weeknumber
     * @param bool $iYear : year
     * @param string $format : date format
     * @return false|string
     */
    public static function getMondayFromWeeknr($iWeek, $iYear = false, $format = "Y-m-d") {
      if ($iYear === false) $iYear = date("Y");
      if ($iWeek < 10) {
        $iWeek = "0" . $iWeek;
      }
      return date($format, strtotime($iYear . 'W' . $iWeek));
    }

    /**
     * Get hours from time (11:25)
     * @param string $time
     * @param int $decimals
     * @return float|int
     */
    public static function getHoursFromTime($time, $decimals = 0) {
      if ($time == "") {
        return 0;
      }
      $t = explode(':', $time);
      if ($decimals == 0) {
        return intval($t[0]);
      }
      return round(intval($t[0]) + intval($t[1]) / 60, $decimals);
    }

    /**
     * Get hours from periode (11:15, 12:16)
     * @param $time1
     * @param $time2
     * @return float|int
     */
    public static function getHoursFromPeriod($time1, $time2) {
      if ($time1 == "") {
        return 0;
      }
      if ($time2 == "") {
        return 0;
      }
      if ($time2 == "0:00" || $time2 == "00:00") {
        $time2 = '24:00';
      }
      $t1 = explode(':', $time1);
      $t2 = explode(':', $time2);
      if ($t1[0] <= $t2[0]) {
        return $t2[0] - $t1[0] + (($t2[1] - $t1[1]) / 60);
      }

      return 0;
    }

    /**
     * Calculate hours between two datetime strings using the DateTime diff function
     * @param string $datetimestring1 (required) format: Y-m-d H:i:s
     * @param string $datetimestring2 (required) format: Y-m-d H:i:s
     * @return float|int
     * @throws Exception
     */
    public static function getHoursFromPeriodDatetime($datetimestring1, $datetimestring2) {
      if ($datetimestring1 == "" || $datetimestring2 == "") {
        return 0;
      }

      $dt1 = new DateTime($datetimestring1);
      $dt2 = new DateTime($datetimestring2);
      $interval = $dt1->diff($dt2);

      return ($interval->h + ($interval->i / 60));
    }

    /**
     * Time format is UNIX timestamp or PHP strtotime compatible strings
     * @param     $time1
     * @param     $time2
     * @param int $precision
     * @return string
     */
    public static function dateDiff($time1, $time2, $precision = 6) {
      // If not numeric then convert texts to unix timestamps
      if (!is_int($time1)) {
        $time1 = strtotime($time1);
      }
      if (!is_int($time2)) {
        $time2 = strtotime($time2);
      }

      // If time1 is bigger than time2
      // Then swap time1 and time2
      if ($time1 > $time2) {
        $ttime = $time1;
        $time1 = $time2;
        $time2 = $ttime;
      }

      // Set up intervals and diffs arrays
      $intervals = ['year', 'month', 'day', 'hour', 'minute', 'second'];
      $diffs = [];

      // Loop thru all intervals
      foreach ($intervals as $interval) {
        // Set default diff to 0
        $diffs[$interval] = 0;
        // Create temp time from time1 and interval
        $ttime = strtotime("+1 " . $interval, $time1);
        // Loop until temp time is smaller than time2
        while ($time2 >= $ttime) {
          $time1 = $ttime;
          $diffs[$interval]++;
          // Create new temp time from time1 and interval
          $ttime = strtotime("+1 " . $interval, $time1);
        }
      }

      $count = 0;
      $times = [];
      // Loop thru all diffs
      foreach ($diffs as $interval => $value) {
        // Break if we have needed precission
        if ($count >= $precision) {
          break;
        }
        // Add value and interval
        // if value is bigger than 0
        if ($value > 0) {
          // Add s if value is not 1
          if ($value != 1) {
            $interval .= "s";
          }
          // Add value and interval to times array
          $times[] = $value . " " . $interval;
          $count++;
        }
      }

      // Return string with times
      return implode(", ", $times);
    }

    /**
     * Howemany weeks between two dates floored
     * @param $date1
     * @param $date2
     * @return float
     */
    public static function datediffInWeeks($date1, $date2) {
      return floor(DateTimeHelper::datediffInDays($date1, $date2) / 7);
    }

    /**
     * Howemany days between two dates floored
     * @param $date1 : format Y-m-d
     * @param $date2 : format Y-m-d
     * @return float
     */
    public static function datediffInDays($date1, $date2) {
      if ($date1 == $date2) return 0;
      if ($date1 > $date2) return DateTimeHelper::datediffInDays($date2, $date1);
      $first = DateTime::createFromFormat('Y-m-d', $date1);
      $second = DateTime::createFromFormat('Y-m-d', $date2);
      return floor($first->diff($second)->days);
    }

    /**
     * Subtracts two year-week
     * @param $yearweek1 ('YYYY-WW')
     * @param $yearweek2 ('YYYY-WW')
     * @return int: diff in weeks
     */
    public static function subtractWeeks($yearweek1, $yearweek2) {
      [$year1, $week1] = explode("-", $yearweek1);
      $yw1 = str_replace("-", "", $yearweek1);

      [$year2, $week2] = explode("-", $yearweek2);
      $yw2 = str_replace("-", "", $yearweek2);

      if ($yw1 == $yw2) {
        return 0;
      }
      elseif ($yw1 > $yw2) {
        [$year1, $week1] = explode("-", $yearweek2);
        $yw1 = str_replace("-", "", $yearweek2);
        [$year2, $week2] = explode("-", $yearweek1);
        $yw2 = str_replace("-", "", $yearweek1);
      }

      //total weeks per year mapping
      $weeks_year_arr = [];
      if (!isset($weeks_year_arr[$year1])) {
        $weeks_year_arr[$year1] = DateTimeHelper::getTotalWeeksInYear($year1);
      }
      if (!isset($weeks_year_arr[$year2])) {
        $weeks_year_arr[$year2] = DateTimeHelper::getTotalWeeksInYear($year2);
      }

      $noweeks = 0;
      while ($yw1 != $yw2) {
        $noweeks++;

        $week1++;
        if ($week1 > $weeks_year_arr[$year1]) {
          $week1 = 1;
          $year1++;
        }

        $yw1 = $year1 . zerofill($week1);
      }

      //    pd($yearweek1 . ' - ' . $yearweek2 . ' = ' . $noweeks);
      return $noweeks;

    }

    /**
     * Get total weeks in year (52 or 53 in for example 2020)
     * @param string|int $year (optional) if empty current year
     * @return int|false total weeks in $year
     */
    public static function getTotalWeeksInYear($year = '') {
      if ($year == "") {
        $year = date('Y');
      }

      $day_of_month = 31;
      $weeks_in_year = 1;
      while ($weeks_in_year == 1) {
        $weeks_in_year = date("W", strtotime($day_of_month . " december " . $year));
        $day_of_month--;
      }

      return $weeks_in_year;
    }

    /**
     * Get days of week in the current language
     * %a = short, %A = long
     * @param string $format
     * @return string[]
     */
    public static function getDaysOfWeek($format = "%A") {
      return [
        DateTimeHelper::strftime($format, strtotime("THIS MONDAY")),
        DateTimeHelper::strftime($format, strtotime("THIS TUESDAY")),
        DateTimeHelper::strftime($format, strtotime("THIS WEDNESDAY")),
        DateTimeHelper::strftime($format, strtotime("THIS THURSDAY")),
        DateTimeHelper::strftime($format, strtotime("THIS FRIDAY")),
        DateTimeHelper::strftime($format, strtotime("THIS SATURDAY")),
        DateTimeHelper::strftime($format, strtotime("THIS SUNDAY")),
      ];
    }

    /**
     * Month count since
     * @param     $year
     * @param int $month
     * @return int
     */
    public static function getNrOfMonthsSince($year, $month = 0) {
      $year = date('Y') - $year;
      $month = date('m') - $month;
      $months = $year * 12 + $month;
      return $months;
    }

    /**
     * Get array with all dates between 2 timestamps
     * @param $timefrom : unix timestamp
     * @param $timeto : unix timestamp
     * @return string[]
     */
    public static function getDaysInPeriod($timefrom, $timeto) {
      $daysinperiod = [];
      $time = $timefrom;
      while ($time <= $timeto) {
        $date = date('Y-m-d', $time);
        $time = strtotime($date . " +1 day");
        $daysinperiod[] = $date;
      }
      return $daysinperiod;
    }

    /**
     * Get last week date (sunday)
     * @param $date
     * @param string $format
     * @return false|string
     */
    public static function getEndOfWeekDate($date, string $format = "Y-m-d 00:00:00") {
      $days = DateTimeHelper::strftime("%w", strtotime($date));
      if ($days != 0) {
        $days = 7 - $days;
      }

      return date($format, strtotime($date . " + 1 DAY + " . $days . " DAY"));
    }

    /**
     * Geef me de datum van de eerste dag van de week (maandag)
     * @param        $date
     * @param string $format
     * @return false|string
     */
    public static function getFirstOfWeekDate($date = null, string $format = "Y-m-d 00:00:00") {
      if ($date == null) $date = date("Y-m-d");
      return date($format, strtotime("-7 DAY", DateTimeHelper::getEndOfWeekDate($date, "U")));
    }

    /**
     * Calculate minutes between 2 timestamps/datetimestrings
     * @param $timein
     * @param $timeout
     * @return float|int
     */
    public static function calculateMinutes($timein, $timeout) {
      if (!is_numeric($timein)) {
        $timein = strtotime($timein);
      }
      if (!is_numeric($timeout)) {
        $timeout = strtotime($timeout);
      }
      return ($timeout - $timein) / 60;
    }

    /**
     * Calculate seconds between 2 timestamps/datetimestrings
     * @param $timein
     * @param $timeout
     * @return float|int
     */
    public static function calculateSeconds($timein, $timeout) {
      if (!is_numeric($timein)) {
        $timein = strtotime($timein);
      }
      if (!is_numeric($timeout)) {
        $timeout = strtotime($timeout);
      }
      return $timeout - $timein;
    }

    /**
     * Convert timestring to millseconds : 12:15:01 => 15643584000
     * @param $timestring
     * @return float|int
     */
    public static function convertTimestringToMilliseconds($timestring) {
      if (strpos($timestring, ":") > -1) {
        $arr = explode(":", $timestring);

        return ($arr[0] * 60 * 60 * 1000) + ($arr[1] * 60 * 1000);
      }

      return 0;
    }

    /**
     * Convert timestring tot seconds: 12:15:01 => 15643584
     * @param $timestring
     * @return float|int
     */
    public static function convertTimestringToSeconds($timestring) {
      if (strpos($timestring, ":") > -1) {
        $arr = explode(":", $timestring);

        return ($arr[0] * 60 * 60) + ($arr[1] * 60) + (isset($arr[2]) ? $arr[2] : 0);
      }

      return 0;
    }

    /**
     * Convert minutes to timestring: 135 min => 02:15
     * @param $minutes
     * @return string
     */
    public static function convertMinutesToTimestring($minutes) {
      $minutes = intval($minutes);
      if($minutes<0) {
        return "-".zerofill(floor($minutes*-1 / 60)) . ":" . zerofill(floor(floor(($minutes*-1) % 60)));
      }
      return zerofill(floor($minutes / 60)) . ":" . zerofill(floor(floor($minutes % 60)));
    }

    /**
     * Convert seconds to timestring: 95 => 00:02 (no seconds shown, seconds floored)
     * Double floor needed for deprecated precision lose warning in php 8.1
     * @param $seconds
     * @return string
     */
    public static function convertSecondsToTimestring($seconds) {
      return zerofill(floor($seconds / (60 * 60))) . ":" . zerofill(floor(floor($seconds / 60) % 60));
    }

    /**
     * Convert milliseconds to timestring: 95000 => 00:02 (no seconds shown, seconds floored)
     * @param $milliseconds
     * @return string
     */
    public static function convertMillisecondsToTimestring($milliseconds) {
      return zerofill(floor($milliseconds / (1000 * 60 * 60))) . ":" . zerofill(floor((($milliseconds / (1000 * 60)) % 60)));
    }

    /**
     * Convert hours to timestring: 3.5 => 03:30
     * @param $uur
     * @return string
     */
    public static function convertHoursToTimestring($uur) {
      $minuten = 0;
      if (round($uur) != $uur) {
        $minuten = ($uur - floor($uur)) * 60;
      }
      $minuten = round($minuten);
      $minuten = sprintf("%02d", $minuten);
      $uur = floor($uur);
      $uur = sprintf("%02d", $uur);
      return $uur . ":" . $minuten;
    }

    /**
     * Return an array with all the times based on the given hours and minutes
     * Example: getTimesOfDay(range(0,23), [0,15,30,45])  returns [00:00, 00:15, 00:30, 00:45, 01:00....]
     *
     * @param array $hours
     * @param array $minutes
     * @return array
     */
    public static function getTimesOfDay(array $hours, array $minutes): array {
      $times_of_day = [];
      foreach ($hours as $_hour) {
        foreach ($minutes as $_minutes) {
          $time = str_pad($_hour, 2, '0', STR_PAD_LEFT);
          $time .= ':';
          $time .= str_pad($_minutes, 2, '0', STR_PAD_LEFT);
          $times_of_day[] = $time;
        }
      }

      return $times_of_day;
    }

    /**
     * Convert a datetime string to a readable text date in the language of the users system
     * default is: 2019-19-11 10:10:00 => 19 november 2019
     *
     * @param ?string $date_string
     * @param string $readable_format
     * @param string $current_format
     * @return string
     */
    public static function convertToReadable(?string $date_string, string $readable_format = '%d %B %Y', string $current_format = 'Y-m-d H:i:s') {
      if(empty($date_string)) return "";
      $date = DateTime::createFromFormat($current_format, $date_string);
      if (empty($date)) {
        return '';
      }
      return DateTimeHelper::strftime($readable_format, strtotime($date->format('Y-m-d H:i:s')));
    }

    /**
     * Returns an array with the 11 dutch holidays
     * of which some are calculated based on the easter day
     *
     * @param string $year
     * @return array
     * @throws Exception
     */
    public static function getDutchHolidays(string $year = ''): array {

      if (!$year) {
        // by default, current year
        $year = (new DateTime())->format('Y');
      }

      // Alle feestdagen kunnen berekend worden, de berekende zijn allemaal
      // afhankelijk van pasen. PHP heeft een functie easter_date().
      // beperking van easter_date()
      if ($year < 1970 || $year > 2037) {
        return [];
      }

      // vaste dagen
      $nieuwjaar = new DateTime($year . '-01-01');
      $bevrijdingsdag = new DateTime($year . '-05-05');
      $kerstmis = new DateTime($year . '-12-25');
      $tweedekerstdag = new DateTime($year . '-12-26');
      $koningsdag = new DateTime($year . '-04-27');

      // Als Koningsdag op zondag valt is het de dag ervoor
      if ($koningsdag->format('w') === '0') {
        $koningsdag->sub(new DateInterval('P1D'));
      }

      //
      $pasen = new DateTime();
      $pasen->setTimestamp(easter_date($year)); // PHP native function for the correct easter date
      $paas_maandag = clone $pasen;
      $paas_maandag->add(new DateInterVal('P1D')); // 1 dag na pasen
      $goede_vrijdag = clone $pasen;
      $goede_vrijdag->sub(new DateInterVal('P2D')); // 2 dag voor pasen
      $hemelvaart = clone $pasen;
      $hemelvaart->add(new DateInterVal('P39D')); // 39 dagen na pasen
      $pinksteren = clone $hemelvaart;
      $pinksteren->add(new DateInterVal('P10D')); // 10 dagen na hemelvaart
      $pinkster_maandag = clone $pinksteren;
      $pinkster_maandag->add(new DateInterVal('P1D')); // 1 dag na pinksteren

      return [
        'nieuwjaar'        => $nieuwjaar->format('Y-m-d'),
        'goede_vrijdag'    => $goede_vrijdag->format('Y-m-d'),
        'pasen'            => $pasen->format('Y-m-d'),
        'paas_maandag'     => $paas_maandag->format('Y-m-d'),
        'koningsdag'       => $koningsdag->format('Y-m-d'),
        'bevrijdingsdag'   => $bevrijdingsdag->format('Y-m-d'),
        'hemelvaart'       => $hemelvaart->format('Y-m-d'),
        'pinksteren'       => $pinksteren->format('Y-m-d'),
        'pinkster_maandag' => $pinkster_maandag->format('Y-m-d'),
        'kerstmis'         => $kerstmis->format('Y-m-d'),
        'tweedekerstdag'   => $tweedekerstdag->format('Y-m-d'),
      ];
    }

    /**
     * Calculate the overlap between 2 periods (4 datetimes)
     * NOTE!! The result can also be negative, check dateinterval->invert for this
     *
     * @param DateTime $date1_start
     * @param DateTime $date1_end
     * @param DateTime $date2_start
     * @param DateTime $date2_end
     * @return DateInterval
     */
    public static function overlapBetweenPeriods(DateTime $date1_start, DateTime $date1_end, DateTime $date2_start, DateTime $date2_end): DateInterval {
      // Figure out which is the later start time
      $last_start = $date1_start->format('Y-m-d H:i:s') >= $date2_start->format('Y-m-d H:i:s') ? $date1_start : $date2_start;

      // Figure out which is the earlier end time
      $first_end = $date1_end->format('Y-m-d H:i:s') <= $date2_end->format('Y-m-d H:i:s') ? $date1_end : $date2_end;

      // return a DateInterval object that expresses the difference between our 2 dates
      /** NOTE!! The result can also be negative, check dateinterval->invert for this **/
      return $last_start->diff($first_end);
    }

    /**
     * Get the number of days in a year
     * @param $year
     * @return int
     */
    public static function getNrOfDaysInYear($year) {
      $days = 0;
      for ($month = 1; $month <= 12; $month++) {
        $days = $days + cal_days_in_month(CAL_GREGORIAN, $month, $year);
      }
      return $days;
    }

    /**
     * Get nr of weeks in month
     * @param $month
     * @param $year
     * @return int
     */
    public static function getNrOfWeeksInMonth($month, $year): int {
      $lastday = date("t", mktime(0, 0, 0, $month, 1, $year));
      $no_of_weeks = 0;
      $count_weeks = 0;
      while ($no_of_weeks < $lastday) {
        $no_of_weeks += 7;
        $count_weeks++;
      }
      return $count_weeks;
    }

    /**
     * Translate a datetime string (day, month, year... ) to a specific language
     * Please note: requires the PHP extension INTL to be loaded
     *
     * @param DateTime $date_time
     * @param string $language 4 chars, as: nl_NL
     * @param string $pattern see: http://userguide.icu-project.org/formatparse/datetime
     * @return string
     * @throws GsdException
     */
    public static function translate(DateTime $date_time, string $language, string $pattern): string {
      if (!extension_loaded('intl')) {
        throw new GsdException('PHP extensions "intl" not loaded');
      }
      $formatter = new IntlDateFormatter($language, IntlDateFormatter::SHORT, IntlDateFormatter::SHORT);
      $formatter->setPattern($pattern);

      return $formatter->format($date_time);
    }

    /**
     * Convert hours and minutes (int 1-60) to float: 4:30 => 4.5
     *
     * @param int $hours
     * @param int $minutes
     * @return float
     */
    public static function convertHoursMinutesToFloat(int $hours, int $minutes): float {
      return $hours + floor(($minutes / 60) * 100) / 100;
    }

    /**
     * Convert float to hours and minutes: 4.5 => 4,30
     *
     * @param float $hours_float
     * @return array
     */
    public static function convertFloatToHoursMinutes(float $hours_float): array {
      $hours = floor($hours_float);
      $minutes = (($hours_float * 60) % 60);

      return [$hours, $minutes];
    }


    /**
     * Create a new datetime object from a datetime object
     *
     * @param DateTimeInterface $date_time
     * @return DateTime
     */
    public static function cloneDateTime(DateTimeInterface $date_time): DateTime {
      return DateTime::createFromFormat('Y-m-d H:i:s', $date_time->format('Y-m-d H:i:s'));
    }


    /**
     * 8.1 deprecates strftime, use this function instead
     *
     * @param string $format
     * @param int|null $timestamp
     * @return string
     */
    public static function strftime(string $format, ?int $timestamp = null) {
      if (is_null($timestamp)) $timestamp = time();
      return strftime($format, $timestamp);
    }

    /**
     * Format db datetime standard, empty on failure
     * Strfrtime is deprecated, so use this function instead
     * @param string|null $datetime : standard datetime string format Y-MM-dd hh:mm, if null wil return ""
     * @param $format : see https://unicode-org.github.io/icu/userguide/format_parse/datetime/ for all possiblities
     * Standard this is formatted:
     *   'EEE d MMMM Y, hh:mm' = ma 1 september 2022, 11:33
     *   'dd MMM Y' = 01 sept 2022
     *   'dd-MM-Y' = 27-09-2022
     * @return  string empty string on failure
     */
    public static function formatDateTime(?string $datetime, string $format = 'dd-MM-Y hh:mm'): string {
      if (empty($datetime) || $datetime == "0000-00-00 00:00:00") return "";
      $new_date = DateTime::createFromFormat("Y-m-d H:i:s", $datetime);
      $fmt = IntlDateFormatter::create(
        Locale::getDefault(),
        IntlDateFormatter::FULL,
        IntlDateFormatter::FULL,
        date_default_timezone_get(),
        IntlDateFormatter::GREGORIAN,
        $format
      );
      return $fmt->format($new_date);
    }

    /**
     * Format db date standard, empty on failure
     * Strfrtime is deprecated, so use this function instead
     * @param string|null $date : standard date string format Y-m-d, if null wil return ""
     * @param $format : see https://unicode-org.github.io/icu/userguide/format_parse/datetime/ for all possiblities
     * Standard this is formatted:
     *   'EEE d MMMM Y' = ma 1 september 2022
     *   'dd MMM Y' = 01 sept 2022
     *   'dd-MM-Y' = 27-09-2022
     * @return  string empty string on failure
     */
    public static function formatDate(?string $date, string $format = 'dd-MM-Y'): string {
      if (empty($date) || $date == "0000-00-00") return "";
      $new_date = DateTime::createFromFormat("Y-m-d", $date);
      $fmt = IntlDateFormatter::create(
        Locale::getDefault(),
        IntlDateFormatter::FULL,
        IntlDateFormatter::FULL,
        date_default_timezone_get(),
        IntlDateFormatter::GREGORIAN,
        $format
      );
      return $fmt->format($new_date);
    }


    /**
     * Format db date standard, not localized, empty string if empty date
     * Strfrtime is deprecated, so use this function instead
     * @param string|null $date : standard date string format Y-m-d, if null wil return ""
     * @param $format : see https://www.php.net/manual/en/datetime.format.php
     * @return  string date string or empty
     */
    public static function formatDbDate(?string $date, string $format = 'd-m-Y'): string {
      if (empty($date) || $date == "0000-00-00" || $date == "0000-00-00 00:00:00") return "";
      return date($format, strtotime($date));
    }

    /**
     * Retrieve all weeks of a year
     * Key = Week_nr
     * Value = DatePeriod, use getStart for startdate of week and getEnd for enddate of week
     *
     * @param int $year
     * @return DatePeriod[]
     */
    public static function getWeeksOfYear(int $year) {
      $interval = new DateInterval('P1W');
      // set to first week
      $start_date = (new DateTime())->setISODate($year, 1, 1);
      $end_date = (new DateTime())->setDate($year, 12, 31);
      $date_range = new DatePeriod($start_date, $interval, $end_date);

      $weeks_in_year = [];
      foreach ($date_range as $date) {
        $last_day_of_week = (DateTime::createFromFormat('Y-m-d', $date->format('Y-m-d')))->modify('+7 days');
        // the ltrim is important, it will remove the leading zero's (01 becomes 1)
        // this way you can compare the key in sql with WEEK(date), which would return 1 and not 01
        $weeks_in_year[ltrim($date->format('W'), '0')] = new DatePeriod($date, new DateInterval('P1W'), $last_day_of_week);
      }

      return $weeks_in_year;
    }

    /**
     * Calculates the amount of workdays (mon-fri) <u>between</u> two dates.
     *
     * @param string|DateTime $start_date Must be in 'd-m-Y' format.
     * @param string|DateTime $end_date Must be in 'd-m-Y' format.
     * @return int
     * @throws Exception
     */
    public static function getWorkDaysDiff(string|DateTime $start_date, string|DateTime $end_date): int {
      if (gettype($start_date) === 'string'){
        $start_date = new DateTime($start_date);
      }
      if (gettype($end_date) === 'string'){
        $end_date = new DateTime($end_date);
      }

      $work_days = 0;
      while ($start_date < $end_date){
        if ($start_date->format('N') !== '6' && $start_date->format('N') !== '7'){
          $work_days++;
        }
        $start_date->modify("+1 day");
      }

      return $work_days;
    }

    /**
     * Get DateTime by day number of the week. 1=monday, 7=sunday
     * Keeps year switch into account!
     * Preserves time of input date
     * @param DateTime $in: a date in the week, not necessary monday.
     * @param int $dayNr
     * @return DateTime
     * @throws Exception
     */
    public static function getDateTimeByDayNr(DateTime $in, int $dayNr = 1): DateTime {
      if($dayNr<1 || $dayNr>7) {
        throw new GsdException('Day number must be between 1 and 7');
      }
      $firstDayOfWeekStr = DateTimeHelper::getFirstOfWeekDate($in->format('Y-m-d H:i:s'), 'Y-m-d H:i:s');
      $newDate = new DateTime($firstDayOfWeekStr);
      $newDate->modify('+' . ($dayNr - 1) . ' DAYS');
      return $newDate;
    }


  }